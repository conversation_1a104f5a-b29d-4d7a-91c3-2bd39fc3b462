"""
Onboarding component for Arien CLI.

This module provides the initial setup wizard for configuring
LLM providers and API keys.
"""

import asyncio
import os
from typing import Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm, Prompt
from rich.text import Text

from ...core.config import config_manager
from ...core.llm_providers.deepseek import DeepseekProvider
from ...core.llm_providers.ollama import OllamaProvider


class OnboardingComponent:
    """
    Onboarding wizard component.
    
    Guides users through:
    - Provider selection
    - API key configuration
    - Model selection
    - Initial testing
    """
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize onboarding component.
        
        Args:
            console: Rich console instance
        """
        self.console = console or Console()
    
    async def run(self) -> None:
        """Run the complete onboarding process."""
        self._show_welcome()
        
        # Configure providers
        await self._configure_deepseek()
        await self._configure_ollama()
        
        # Test connections
        await self._test_providers()
        
        # Set default provider
        self._set_default_provider()
        
        self._show_completion()
    
    def _show_welcome(self) -> None:
        """Show welcome message."""
        welcome_text = Text()
        welcome_text.append("Welcome to ", style="white")
        welcome_text.append("Arien CLI", style="bold cyan")
        welcome_text.append("!\n\n", style="white")
        welcome_text.append("This setup wizard will help you configure LLM providers.\n", style="dim")
        welcome_text.append("You can always reconfigure later using ", style="dim")
        welcome_text.append("arien config", style="bold")
        welcome_text.append(".", style="dim")
        
        panel = Panel(
            welcome_text,
            title="Setup Wizard",
            border_style="cyan",
            padding=(1, 2)
        )
        
        self.console.print(panel)
        self.console.print()
    
    async def _configure_deepseek(self) -> None:
        """Configure Deepseek provider."""
        self.console.print("[bold]Configuring Deepseek Provider[/bold]")
        self.console.print("Deepseek provides fast and efficient chat models.")
        self.console.print("Visit https://platform.deepseek.com to get your API key.\n")
        
        configure = Confirm.ask("Would you like to configure Deepseek?", default=True)
        
        if configure:
            api_key = Prompt.ask(
                "Enter your Deepseek API key",
                password=True,
                show_default=False
            )
            
            if api_key:
                # Test the API key
                self.console.print("Testing API key...", end=" ")
                
                try:
                    provider = DeepseekProvider(api_key=api_key)
                    await provider.initialize()
                    is_valid = await provider.validate_connection()
                    await provider.close()

                    if is_valid:
                        self.console.print("[green]✅ Valid[/green]")

                        # Save configuration
                        config_manager.update_provider_config(
                            "deepseek",
                            api_key=api_key,
                            enabled=True
                        )

                        self.console.print("[green]Deepseek configured successfully![/green]")
                    else:
                        self.console.print("[red]❌ Invalid[/red]")
                        self.console.print("[yellow]Please check your API key and try again.[/yellow]")
                        self.console.print("[dim]You can reconfigure later with: arien config --set-provider deepseek --api-key YOUR_KEY[/dim]")
                        self.console.print("[yellow]Skipping Deepseek configuration[/yellow]")

                except Exception as e:
                    self.console.print(f"[red]❌ Error: {str(e)}[/red]")
                    self.console.print("[yellow]Please check your API key and network connection.[/yellow]")
                    self.console.print("[dim]You can reconfigure later with: arien config --set-provider deepseek --api-key YOUR_KEY[/dim]")
                    self.console.print("[yellow]Skipping Deepseek configuration[/yellow]")
            else:
                self.console.print("[yellow]No API key provided, skipping Deepseek[/yellow]")
        else:
            self.console.print("[dim]Skipping Deepseek configuration[/dim]")
        
        self.console.print()
    
    async def _configure_ollama(self) -> None:
        """Configure Ollama provider."""
        self.console.print("[bold]Configuring Ollama Provider[/bold]")
        self.console.print("Ollama provides local LLM inference (no API key required).")
        self.console.print("Visit https://ollama.ai to install Ollama locally.\n")
        
        configure = Confirm.ask("Would you like to configure Ollama?", default=True)
        
        if configure:
            base_url = Prompt.ask(
                "Enter Ollama base URL",
                default="http://localhost:11434"
            )
            
            # Test connection
            self.console.print("Testing connection...", end=" ")
            
            try:
                provider = OllamaProvider(base_url=base_url)
                await provider.initialize()
                is_valid = await provider.validate_connection()
                
                if is_valid:
                    self.console.print("[green]✅ Connected[/green]")
                    
                    # Get available models
                    models = provider.available_models
                    if models:
                        self.console.print(f"Found {len(models)} models: {', '.join(models[:3])}")
                        if len(models) > 3:
                            self.console.print(f"  (+{len(models) - 3} more)")
                    
                    # Save configuration
                    config_manager.update_provider_config(
                        "ollama",
                        base_url=base_url,
                        models=models,
                        enabled=True
                    )
                    
                    self.console.print("[green]Ollama configured successfully![/green]")
                else:
                    self.console.print("[red]❌ Connection failed[/red]")
                    self.console.print("[yellow]Skipping Ollama configuration[/yellow]")
                
                await provider.close()
            
            except Exception as e:
                self.console.print(f"[red]❌ Error: {e}[/red]")
                self.console.print("[yellow]Skipping Ollama configuration[/yellow]")
        else:
            self.console.print("[dim]Skipping Ollama configuration[/dim]")
        
        self.console.print()
    
    async def _test_providers(self) -> None:
        """Test all configured providers."""
        self.console.print("[bold]Testing Configured Providers[/bold]")
        
        config = config_manager.load_config()
        working_providers = []
        
        for name, provider_config in config.providers.items():
            if not provider_config.enabled:
                continue
            
            self.console.print(f"Testing {name.title()}...", end=" ")
            
            try:
                if name == "deepseek":
                    provider = DeepseekProvider(
                        api_key=provider_config.api_key,
                        base_url=provider_config.base_url
                    )
                elif name == "ollama":
                    provider = OllamaProvider(
                        base_url=provider_config.base_url
                    )
                else:
                    self.console.print("[yellow]❓ Unknown[/yellow]")
                    continue
                
                await provider.initialize()
                is_valid = await provider.validate_connection()
                await provider.close()
                
                if is_valid:
                    self.console.print("[green]✅ Working[/green]")
                    working_providers.append(name)
                else:
                    self.console.print("[red]❌ Failed[/red]")
            
            except Exception as e:
                self.console.print(f"[red]❌ Error[/red]")
        
        if working_providers:
            self.console.print(f"\n[green]{len(working_providers)} provider(s) configured successfully![/green]")
        else:
            self.console.print("\n[yellow]No working providers configured.[/yellow]")
            self.console.print("You can configure providers later using 'arien config'")
        
        self.console.print()
    
    def _set_default_provider(self) -> None:
        """Set default provider."""
        config = config_manager.load_config()
        
        # Find enabled providers
        enabled_providers = [
            name for name, provider_config in config.providers.items()
            if provider_config.enabled
        ]
        
        if not enabled_providers:
            self.console.print("[yellow]No providers configured, using defaults[/yellow]")
            return
        
        if len(enabled_providers) == 1:
            default_provider = enabled_providers[0]
            self.console.print(f"Setting {default_provider.title()} as default provider")
        else:
            self.console.print("[bold]Choose Default Provider[/bold]")
            for i, provider in enumerate(enabled_providers, 1):
                self.console.print(f"  {i}. {provider.title()}")
            
            while True:
                try:
                    choice = Prompt.ask(
                        "Select default provider",
                        choices=[str(i) for i in range(1, len(enabled_providers) + 1)],
                        default="1"
                    )
                    default_provider = enabled_providers[int(choice) - 1]
                    break
                except (ValueError, IndexError):
                    self.console.print("[red]Invalid choice[/red]")
        
        # Get default model for provider
        provider_config = config.providers[default_provider]
        default_model = provider_config.default_model
        
        if not default_model and provider_config.models:
            default_model = provider_config.models[0]
        
        # Update default session config
        config.default_session.provider = default_provider
        if default_model:
            config.default_session.model = default_model
        
        config_manager.save_config()
        
        self.console.print(f"[green]Default provider set to {default_provider.title()}[/green]")
        if default_model:
            self.console.print(f"[green]Default model set to {default_model}[/green]")
        
        self.console.print()
    
    def _show_completion(self) -> None:
        """Show completion message."""
        completion_text = Text()
        completion_text.append("Setup completed! ", style="green bold")
        completion_text.append("🎉\n\n", style="green")
        completion_text.append("You can now start using Arien CLI:\n\n", style="white")
        completion_text.append("• ", style="dim")
        completion_text.append("arien chat", style="bold cyan")
        completion_text.append(" - Start interactive chat\n", style="dim")
        completion_text.append("• ", style="dim")
        completion_text.append("arien config", style="bold cyan")
        completion_text.append(" - Manage configuration\n", style="dim")
        completion_text.append("• ", style="dim")
        completion_text.append("arien providers", style="bold cyan")
        completion_text.append(" - Manage providers\n", style="dim")
        completion_text.append("• ", style="dim")
        completion_text.append("arien --help", style="bold cyan")
        completion_text.append(" - Show all commands", style="dim")
        
        panel = Panel(
            completion_text,
            title="Setup Complete",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print(panel)
